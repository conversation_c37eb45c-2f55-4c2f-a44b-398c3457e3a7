# 医生选择功能和发起会诊接口对接实施总结

## 实施概述

已成功实现医生选择功能和发起会诊的接口对接，将原有的专家选择功能改为医生选择，并支持多选功能。

## 主要更新内容

### 1. MeetingInfoForm.vue 组件更新

#### 功能变更
- **专家选择 → 医生选择**: 将原有的专家选择功能改为医生选择
- **单选 → 多选**: 支持选择多个医生参与会诊
- **接口更新**: 使用 `listDoctor` 接口替代 `listExperts` 接口

#### 具体修改
- 导入语句: `import { listDoctor } from "@/api/system/doctor"`
- 表单字段更新:
  - `selectedExpert` → `selectedDoctors` (数组)
  - 新增 `selectedDoctorsText` 用于显示选中医生名称
- 弹窗更新:
  - 标题: "选择专家" → "选择医生"
  - 表格类型: `type="radio"` → `type="selection"`
  - 列名更新: 专家姓名 → 医生姓名
- 方法更新:
  - `handleExpertSelect()` → `handleDoctorSelect()`
  - `loadExpertList()` → `loadDoctorList()`
  - `confirmExpertSelection()` → `confirmDoctorSelection()`
  - 新增 `handleDoctorSelectionChange()` 处理多选

### 2. meeting.js API 接口新增

#### 新增接口函数
```javascript
// 发起会诊
export function sendMeeting(data) {
  return request({
    url: "/system/meeting/send",
    method: "post",
    data: data,
  });
}
```

### 3. create.vue 会诊创建页面更新

#### 提交逻辑更新
- 导入新接口: `import { sendMeeting } from "@/api/system/meeting"`
- 数据格式适配: 构建符合 `/system/meeting/send` 接口规范的数据结构
- 参数映射:
  - `address`: 会议地址
  - `consultType`: 会诊类型 (0线上 1线下)
  - `diseaseCode`: 病例id
  - `expectEndTime`: 预期会议结束时间
  - `expectStartTime`: 预期会议开始时间
  - `idList`: 会诊医生id列表
  - `memId`: 患者id
  - `memName`: 患者名称

### 4. meetingUtils.js 验证逻辑更新

#### 表单验证更新
- 验证条件: `selectedExpert` → `selectedDoctors` 数组验证
- 确保至少选择一位医生

## 接口规范

### 发起会诊接口
- **路径**: `POST /system/meeting/send`
- **参数格式**:
```json
{
  "address": "会议地址",
  "consultType": 0,
  "diseaseCode": 0,
  "expectEndTime": "2024-01-01 18:00:00",
  "expectStartTime": "2024-01-01 16:00:00",
  "idList": [1, 2, 3],
  "memId": 123,
  "memName": "患者姓名"
}
```

### 医生列表接口
- **路径**: `GET /doctor`
- **使用函数**: `listDoctor(query)`
- **响应格式**: 支持 `response.rows` 和 `response.data` 两种格式

## 用户体验改进

### 1. 多选支持
- 用户可以选择多个医生参与会诊
- 选中的医生名称以逗号分隔显示在输入框中

### 2. 错误处理
- 完善的错误提示和加载状态
- 表单验证确保必填字段完整

### 3. 数据一致性
- 确保选中的医生信息正确传递给后端接口
- 支持医生基本信息显示（姓名、科室、职称等）

## 兼容性说明

### 向后兼容
- 保持原有组件的 props 接口不变
- 表单验证和提交流程保持一致

### 数据格式兼容
- 支持多种患者信息字段名称 (`name`, `patientName`)
- 兼容不同的响应数据格式 (`rows`, `data`)

## 测试建议

1. **医生选择功能测试**
   - 验证医生列表正确加载
   - 测试多选功能
   - 确认选中医生信息正确显示

2. **会诊发起功能测试**
   - 验证表单验证逻辑
   - 测试接口调用和数据传递
   - 确认成功/失败提示正常

3. **边界情况测试**
   - 网络异常处理
   - 空数据处理
   - 表单验证边界值

## 后续优化建议

1. **性能优化**
   - 医生列表分页加载
   - 搜索和筛选功能

2. **用户体验**
   - 医生信息详情展示
   - 选择历史记录

3. **功能扩展**
   - 医生专业领域匹配
   - 会诊时间冲突检测
