# SupplementInfoForm 动态表单使用指南

## 功能概述

SupplementInfoForm 组件现在支持动态表单渲染，可以根据后端返回的 `values` 数据自动生成不同类型的表单组件。

## 数据结构

### 输入数据格式

```javascript
const supplementData = {
  // 原有的静态字段
  samplingTime: "2025-08-04 16:04:31",
  receiveTime: "2025-08-04 17:00:00",
  testMaterial: "血液样本",
  testDepartment: "检验科",
  testDoctor: "张医生",
  meetingRecord: "会议记录内容",
  reportFiles: [],

  // 新增的动态字段数据
  values: [
    {
      id: 1,
      dttId: 10,
      membId: 580,
      labelName: "是否可以接受异地会诊",
      labelValue: "接受",
      formType: "3",
      sort: 1,
      notNull: 1,
      items: "接受;不接受;待定",
      remarks: null,
      createUser: null,
      createTime: "2025-08-04 16:04:31",
      updataUser: null,
      updataTime: null,
      diseaseCode: "001100400436",
    },
    {
      id: 2,
      dttId: 10,
      membId: 580,
      labelName: "血压范围1-200",
      labelValue: "血压正常",
      formType: "6",
      sort: 2,
      notNull: 0,
      items: null,
      remarks: null,
      createUser: null,
      updataTime: null,
      diseaseCode: "001100400436",
    },
  ],
};
```

## 表单类型映射

| formType | 组件类型            | 说明         | 示例                 | 需要选项 | 布局  |
| -------- | ------------------- | ------------ | -------------------- | -------- | ----- |
| "1"      | el-input            | 单行文本输入 | 姓名、地址等         | 否       | 12 列 |
| "2"      | el-input (textarea) | 多行文本输入 | 备注、描述等         | 否       | 24 列 |
| "3"      | el-radio-group      | 单选按钮组   | 性别、是否同意等     | 是       | 12 列 |
| "4"      | el-checkbox-group   | 多选框组     | 兴趣爱好、症状等     | 是       | 24 列 |
| "5"      | el-date-picker      | 时间选择器   | 发病时间、检查时间等 | 否       | 12 列 |
| "6"      | el-cascader         | 级联选择器   | 地区选择、分类选择等 | 是       | 12 列 |
| "7"      | el-upload           | 上传组件     | 检查报告、图片等     | 否       | 24 列 |

## 字段属性说明

- **labelName**: 表单项的标签文本
- **labelValue**: 表单项的当前值
- **formType**: 表单组件类型（对应上表）
- **notNull**: 是否必填（1=必填，0=非必填）
- **items**: 选项数据，用分号分隔（如 "选项 1;选项 2;选项 3"）
- **sort**: 排序字段，数字越小越靠前

## 使用示例

### 方式一：直接传入 values 数组（推荐）

```vue
<template>
  <div>
    <!-- 直接传入 values 数组 -->
    <SupplementInfoForm v-model="valuesArray" :readonly="false" :show-tooltip="true" />
  </div>
</template>

<script>
import SupplementInfoForm from "@/components/Meeting/SupplementInfoForm.vue";

export default {
  components: {
    SupplementInfoForm,
  },
  data() {
    return {
      // 直接使用 values 数组
      valuesArray: [
        {
          id: 1,
          labelName: "是否可以接受异地会诊",
          labelValue: "接受",
          formType: "3",
          sort: 1,
          notNull: 1,
          items: "接受;不接受;待定",
        },
        {
          id: 2,
          labelName: "血压值",
          labelValue: "120",
          formType: "6",
          sort: 2,
          notNull: 1,
          items: null,
        },
      ],
    };
  },
};
</script>
```

### 方式二：传入包含 values 属性的对象（向后兼容）

```vue
<template>
  <div>
    <SupplementInfoForm v-model="supplementInfo" :readonly="false" :show-tooltip="true" />
  </div>
</template>

<script>
import SupplementInfoForm from "@/components/Meeting/SupplementInfoForm.vue";

export default {
  components: {
    SupplementInfoForm,
  },
  data() {
    return {
      supplementInfo: {
        // 动态字段配置
        values: [
          {
            id: 1,
            labelName: "是否可以接受异地会诊",
            labelValue: "接受",
            formType: "3",
            sort: 1,
            notNull: 1,
            items: "接受;不接受;待定",
          },
          {
            id: 2,
            labelName: "血压值",
            labelValue: "120",
            formType: "6",
            sort: 2,
            notNull: 1,
            items: null,
          },
        ],
      },
    };
  },
};
</script>
```

### 只读模式

```vue
<SupplementInfoForm v-model="supplementInfo" :readonly="true" :show-tooltip="false" />
```

## 数据输出

组件会输出动态表单的数据：

```javascript
// 输出的数据结构（直接输出 dynamicFormData 对象）
{
  "field_1": "接受",      // 对应 id=1 的字段值
  "field_2": 120,         // 对应 id=2 的字段值
  "field_3": ["选项1", "选项2"],  // 多选字段的值
  // ... 其他动态字段
}
```

### 字段键名规则

- 字段键名格式：`field_${id}`
- 其中 `id` 是动态字段配置中的 `id` 值
- 例如：`id: 1` 的字段对应键名 `field_1`

## 样式自定义

动态表单会根据字段类型自动调整布局：

- **单行文本、下拉选择、数字输入**: 占用 12 列（一行两个）
- **多行文本、多选框**: 占用 24 列（独占一行）
- **单选按钮**: 占用 12 列

## 验证规则

- 根据 `notNull` 字段自动设置必填验证
- 支持自定义验证规则扩展

## 注意事项

1. **字典依赖**: 组件依赖 `disease_form_type` 字典，确保系统中已配置
2. **数据格式**: `items` 字段使用分号分隔选项，请确保数据格式正确
3. **字段 ID**: 动态表单数据使用 `field_${id}` 作为键名
4. **排序**: 字段按 `sort` 值升序排列显示

## 扩展开发

如需添加新的表单类型：

1. 在 `getComponentType` 方法中添加新的映射
2. 在 `getComponentProps` 方法中配置组件属性
3. 在 `getDefaultValue` 方法中设置默认值
4. 在模板中添加对应的选项渲染逻辑

## 调试

组件支持 Vue DevTools 调试，可以查看：

- `dynamicFields`: 动态字段配置
- `dynamicFormData`: 动态表单数据
- `sortedDynamicFields`: 排序后的字段列表
