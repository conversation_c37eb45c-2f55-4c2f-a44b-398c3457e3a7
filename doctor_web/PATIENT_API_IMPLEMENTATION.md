# 患者信息API接口实施总结

## 实施概述

已成功创建专门的患者信息API文件，集中管理所有患者相关的接口功能，提供完整的患者信息查询、管理和搜索功能。

## 主要更新内容

### 1. 新建患者API文件

#### 文件位置
**文件**: `doctor_web/src/api/system/patient.js`

#### 核心接口功能

##### 基础查询接口
```javascript
// 获取患者基础信息（根据接口文档）
export function getMemBaseInfo(code)

// 查询患者详细信息
export function getPatientDetail(id)

// 获取患者基本信息（兼容原有接口）
export function getPatientInfo(patientId)
```

##### 列表和搜索接口
```javascript
// 查询患者列表（支持分页和搜索）
export function listPatients(query)

// 患者搜索功能
export function searchPatients(keyword)

// 查询医生客户端患者列表
export function listDoctorClientPatients(query)
```

##### 管理接口
```javascript
// 新增患者信息
export function addPatient(data)

// 修改患者信息
export function updatePatient(data)

// 删除患者信息
export function delPatient(id)

// 批量删除患者信息
export function delPatients(ids)
```

##### 扩展功能接口
```javascript
// 导出患者信息
export function exportPatients(query)

// 获取患者统计信息
export function getPatientStats()

// 根据条件查询患者数量
export function getPatientCount(query)
```

### 2. 接口规范说明

#### 核心接口详情

**getMemBaseInfo(code)**
- **用途**: 根据接口文档获取患者基础信息
- **路径**: `GET /system/meeting/getMemBaseInfo`
- **参数**: `code` - 患者编码
- **返回**: 患者基础信息对象

**listPatients(query)**
- **用途**: 分页查询患者列表
- **路径**: `GET /system/patient/list`
- **参数**: 
  ```javascript
  {
    pageNum: 1,        // 页码
    pageSize: 10,      // 每页数量
    name: "",          // 患者姓名（模糊查询）
    gender: "",        // 性别
    hospitalName: "",  // 医院名称
    doctorName: ""     // 医生姓名
  }
  ```

**searchPatients(keyword)**
- **用途**: 根据关键词搜索患者
- **路径**: `GET /system/patient/search`
- **参数**: `keyword` - 搜索关键词
- **返回**: 匹配的患者列表

### 3. 使用示例

#### 基本使用
```javascript
import { 
  getMemBaseInfo, 
  listPatients, 
  getPatientDetail,
  searchPatients 
} from "@/api/system/patient";

// 获取患者基础信息
async function loadPatientBaseInfo(code) {
  try {
    const response = await getMemBaseInfo(code);
    return response.data;
  } catch (error) {
    console.error("获取患者基础信息失败:", error);
    throw error;
  }
}

// 查询患者列表
async function loadPatientList(params) {
  try {
    const response = await listPatients(params);
    return {
      list: response.rows || response.data || [],
      total: response.total || 0
    };
  } catch (error) {
    console.error("获取患者列表失败:", error);
    throw error;
  }
}

// 搜索患者
async function searchPatientByKeyword(keyword) {
  try {
    const response = await searchPatients(keyword);
    return response.data || [];
  } catch (error) {
    console.error("搜索患者失败:", error);
    throw error;
  }
}
```

#### 在组件中使用
```vue
<template>
  <div>
    <!-- 患者搜索 -->
    <el-input 
      v-model="searchKeyword" 
      placeholder="请输入患者姓名或编码"
      @input="handleSearch"
    />
    
    <!-- 患者列表 -->
    <el-table :data="patientList" v-loading="loading">
      <el-table-column prop="name" label="姓名" />
      <el-table-column prop="gender" label="性别" />
      <el-table-column prop="age" label="年龄" />
      <el-table-column prop="hospitalName" label="医院" />
    </el-table>
    
    <!-- 分页 -->
    <el-pagination
      :current-page="queryParams.pageNum"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handlePageChange"
    />
  </div>
</template>

<script>
import { listPatients, searchPatients } from "@/api/system/patient";

export default {
  data() {
    return {
      patientList: [],
      loading: false,
      searchKeyword: "",
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      }
    };
  },
  methods: {
    // 加载患者列表
    async loadPatientList() {
      this.loading = true;
      try {
        const response = await listPatients(this.queryParams);
        this.patientList = response.rows || response.data || [];
        this.total = response.total || 0;
      } catch (error) {
        this.$message.error("获取患者列表失败");
      } finally {
        this.loading = false;
      }
    },
    
    // 搜索患者
    async handleSearch() {
      if (this.searchKeyword) {
        try {
          const response = await searchPatients(this.searchKeyword);
          this.patientList = response.data || [];
        } catch (error) {
          this.$message.error("搜索患者失败");
        }
      } else {
        this.loadPatientList();
      }
    },
    
    // 分页处理
    handlePageChange(page) {
      this.queryParams.pageNum = page;
      this.loadPatientList();
    }
  },
  created() {
    this.loadPatientList();
  }
};
</script>
```

### 4. 向后兼容性

#### 保持现有功能
- `meeting.js` 中的 `getPatientInfo` 接口保留，添加注释说明已迁移
- 现有组件无需修改即可正常工作
- 新功能通过新的患者API文件提供

#### 迁移建议
- 新开发的功能建议使用 `patient.js` 中的接口
- 现有功能可以逐步迁移到新的API文件
- 保持接口参数和返回格式的一致性

### 5. 错误处理

#### 统一错误处理
```javascript
// 标准错误处理模式
async function handlePatientAPI(apiCall) {
  try {
    const response = await apiCall();
    return {
      success: true,
      data: response.data || response.rows || [],
      total: response.total || 0
    };
  } catch (error) {
    console.error("患者API调用失败:", error);
    return {
      success: false,
      error: error.message || "操作失败",
      data: []
    };
  }
}
```

### 6. 数据格式兼容

#### 支持多种响应格式
- `response.data` - 标准数据格式
- `response.rows` - 分页数据格式
- `response.list` - 列表数据格式
- 自动处理空数据情况

### 7. 扩展功能

#### 统计和分析
- `getPatientStats()` - 获取患者统计信息
- `getPatientCount(query)` - 根据条件统计患者数量
- `exportPatients(query)` - 导出患者信息

#### 批量操作
- `delPatients(ids)` - 批量删除患者
- 支持批量更新和导入功能

## 测试建议

### 1. 基础功能测试
- 验证所有接口的正常调用
- 测试参数传递和返回数据格式
- 确认错误处理机制

### 2. 兼容性测试
- 验证现有功能正常工作
- 测试新旧接口的数据一致性
- 确认组件引用更新正确

### 3. 性能测试
- 测试大数据量的列表查询
- 验证搜索功能的响应速度
- 检查分页功能的效率

## 后续优化建议

### 1. 缓存机制
- 添加患者信息缓存
- 实现智能刷新策略

### 2. 高级搜索
- 支持多条件组合搜索
- 添加搜索历史记录

### 3. 数据同步
- 实现实时数据更新
- 添加数据变更通知
