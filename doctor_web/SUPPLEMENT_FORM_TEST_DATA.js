// SupplementInfoForm 组件测试数据
// 包含所有 7 种 formType 的测试用例

export const testFormData = [
  {
    id: 1,
    dttId: 10,
    membId: 580,
    labelName: "患者姓名",
    labelValue: "张三",
    formType: "1", // 单行文本输入
    sort: 1,
    notNull: 1,
    items: null,
    remarks: null,
    createUser: null,
    createTime: "2025-08-04 16:04:31",
    updataUser: null,
    updataTime: null,
    diseaseCode: "001100400436"
  },
  {
    id: 2,
    dttId: 10,
    membId: 580,
    labelName: "病情描述",
    labelValue: "患者主要症状为头痛、发热",
    formType: "2", // 多行文本输入
    sort: 2,
    notNull: 1,
    items: null,
    remarks: null,
    createUser: null,
    createTime: "2025-08-04 16:04:31",
    updataUser: null,
    updataTime: null,
    diseaseCode: "001100400436"
  },
  {
    id: 3,
    dttId: 10,
    membId: 580,
    labelName: "是否可以接受异地会诊",
    labelValue: "接受",
    formType: "3", // 单选按钮组
    sort: 3,
    notNull: 1,
    items: "接受;不接受;待定",
    remarks: null,
    createUser: null,
    createTime: "2025-08-04 16:04:31",
    updataUser: null,
    updataTime: null,
    diseaseCode: "001100400436"
  },
  {
    id: 4,
    dttId: 10,
    membId: 580,
    labelName: "主要症状",
    labelValue: "头痛,发热",
    formType: "4", // 多选框组
    sort: 4,
    notNull: 0,
    items: "头痛;发热;咳嗽;乏力;恶心",
    remarks: null,
    createUser: null,
    createTime: "2025-08-04 16:04:31",
    updataUser: null,
    updataTime: null,
    diseaseCode: "001100400436"
  },
  {
    id: 5,
    dttId: 10,
    membId: 580,
    labelName: "发病时间",
    labelValue: "2025-08-01 10:30:00",
    formType: "5", // 时间选择器
    sort: 5,
    notNull: 1,
    items: null,
    remarks: null,
    createUser: null,
    createTime: "2025-08-04 16:04:31",
    updataUser: null,
    updataTime: null,
    diseaseCode: "001100400436"
  },
  {
    id: 6,
    dttId: 10,
    membId: 580,
    labelName: "所在地区",
    labelValue: "广东省,广州市,天河区",
    formType: "6", // 级联选择器
    sort: 6,
    notNull: 1,
    items: "广东省;广西省;湖南省;湖北省",
    remarks: null,
    createUser: null,
    createTime: "2025-08-04 16:04:31",
    updataUser: null,
    updataTime: null,
    diseaseCode: "001100400436"
  },
  {
    id: 7,
    dttId: 10,
    membId: 580,
    labelName: "相关检查报告",
    labelValue: "",
    formType: "7", // 上传组件
    sort: 7,
    notNull: 0,
    items: null,
    remarks: null,
    createUser: null,
    createTime: "2025-08-04 16:04:31",
    updataUser: null,
    updataTime: null,
    diseaseCode: "001100400436"
  }
];

// 使用示例
export const usageExample = {
  template: `
    <SupplementInfoForm 
      v-model="formData" 
      :readonly="false" 
      :show-tooltip="true" 
    />
  `,
  data: {
    formData: testFormData
  }
};

// 表单类型说明
export const formTypeDescription = {
  "1": {
    name: "单行文本输入",
    component: "el-input",
    needsOptions: false,
    span: 12,
    description: "用于输入简短的文本信息，如姓名、地址等"
  },
  "2": {
    name: "多行文本输入", 
    component: "el-input (textarea)",
    needsOptions: false,
    span: 24,
    description: "用于输入较长的文本信息，如病情描述、备注等"
  },
  "3": {
    name: "单选按钮组",
    component: "el-radio-group",
    needsOptions: true,
    span: 12,
    description: "用于从多个选项中选择一个，选项通过 items 字段提供"
  },
  "4": {
    name: "多选框组",
    component: "el-checkbox-group", 
    needsOptions: true,
    span: 24,
    description: "用于从多个选项中选择多个，选项通过 items 字段提供"
  },
  "5": {
    name: "时间选择器",
    component: "el-date-picker",
    needsOptions: false,
    span: 12,
    description: "用于选择日期和时间"
  },
  "6": {
    name: "级联选择器",
    component: "el-cascader",
    needsOptions: true,
    span: 12,
    description: "用于层级选择，如地区选择等"
  },
  "7": {
    name: "上传组件",
    component: "el-upload",
    needsOptions: false,
    span: 24,
    description: "用于上传文件，如检查报告、图片等"
  }
};

// 验证函数
export function validateFormTypes() {
  const supportedTypes = ["1", "2", "3", "4", "5", "6", "7"];
  const testTypes = testFormData.map(item => item.formType);
  
  console.log("支持的表单类型:", supportedTypes);
  console.log("测试数据包含的类型:", testTypes);
  
  const missingTypes = supportedTypes.filter(type => !testTypes.includes(type));
  const extraTypes = testTypes.filter(type => !supportedTypes.includes(type));
  
  if (missingTypes.length > 0) {
    console.warn("缺少测试数据的类型:", missingTypes);
  }
  
  if (extraTypes.length > 0) {
    console.warn("不支持的表单类型:", extraTypes);
  }
  
  return {
    isComplete: missingTypes.length === 0 && extraTypes.length === 0,
    missingTypes,
    extraTypes
  };
}

// 导出默认测试数据
export default testFormData;
