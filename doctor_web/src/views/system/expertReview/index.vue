<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="患者名称：" prop="patientName">
        <el-input
          v-model="queryParams.patientName"
          placeholder=""
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable style="width: 200px">
          <el-option label="待审核" value="0" />
          <el-option label="已审核" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" @click="handleQuery">查询</el-button>
        <el-button size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="meetingList" class="consultation-table">
      <el-table-column label="序号" align="center" width="60">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="患者名称" align="center" prop="patientName" />
      <el-table-column label="疾病种类" align="center" prop="diseaseType" />
      <el-table-column label="会议类型" align="center" prop="consultType">
        <template slot-scope="scope">
          <span>{{ scope.row.consultType === 0 ? "线上" : "线下" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预期会议开始时间" align="center" prop="expectStartTime" width="150" />
      <el-table-column label="审核时间" align="center" prop="auditTime" width="120" />
      <el-table-column label="添加会议时间" align="center" prop="addMeetingTime" width="120" />
      <el-table-column label="完成时间" align="center" prop="completeTime" width="120" />
      <el-table-column label="会议地址/链接" align="center" prop="address" />
      <el-table-column label="状态" align="center" prop="auditStatus">
        <template slot-scope="scope">
          <span>{{ scope.row.auditStatus === 1 ? "已审核" : "待审核" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" class="expert-btn" @click="handleViewExperts(scope.row)"
            >查看专家</el-button
          >
          <el-button
            size="mini"
            type="text"
            class="review-btn"
            @click="handleReview(scope.row)"
            :disabled="scope.row.auditStatus === 1"
          >
            {{ scope.row.auditStatus === 1 ? "已审核" : "审核" }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看专家弹窗 -->
    <ExpertViewDialog
      :visible.sync="expertDialogVisible"
      :consultation-data="currentConsultation"
      :meeting-id="currentConsultation && currentConsultation.id"
    />

    <!-- 审核弹窗 -->
    <el-dialog
      title="审核"
      :visible.sync="reviewDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <div class="review-dialog-content">
        <!-- 会议信息展示 -->
        <div class="meeting-info-section">
          <div class="info-row">
            <span class="info-label">* 患者姓名：</span>
            <span class="info-value">{{ currentReviewData.patientName || "李明照" }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">* 疾病种类：</span>
            <span class="info-value">{{ currentReviewData.diseaseType || "结构缺陷" }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">* 会议类型：</span>
            <span class="info-value">{{ currentReviewData.consultType === 0 ? "线上" : "线下" }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">* 会议地址/链接：</span>
            <span class="info-value">{{ currentReviewData.address || "武汉洪山区光谷总部国际时代二期2栋801-3" }}</span>
          </div>
        </div>

        <!-- 审核表单 -->
        <el-form :model="reviewForm" :rules="reviewRules" ref="reviewForm" label-width="100px">
          <el-form-item label="审核：" prop="auditResult" required>
            <el-radio-group v-model="reviewForm.auditResult">
              <el-radio :label="1">同意</el-radio>
              <el-radio :label="0">驳回</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            label="驳回原因："
            prop="rejectReason"
            :required="reviewForm.auditResult === 0"
            v-show="reviewForm.auditResult === 0"
          >
            <el-input
              v-model="reviewForm.rejectReason"
              type="textarea"
              :rows="4"
              placeholder="请输入驳回原因"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelReview">取消</el-button>
        <el-button type="primary" @click="handleConfirmReview" :loading="reviewSubmitting">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listExpertReview, auditMeeting } from "@/api/system/expertReview";
import ExpertViewDialog from "@/components/ExpertViewDialog/index.vue";

export default {
  name: "ExpertReview",
  components: {
    ExpertViewDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会诊管理表格数据
      meetingList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        patientName: null,
        status: null,
      },
      // 查看专家弹窗控制
      expertDialogVisible: false,
      // 当前选中的会诊记录
      currentConsultation: null,
      // 审核弹窗控制
      reviewDialogVisible: false,
      // 当前审核的数据
      currentReviewData: {},
      // 审核表单数据
      reviewForm: {
        auditResult: null, // 1: 同意, 0: 驳回
        rejectReason: "",
      },
      // 审核表单验证规则
      reviewRules: {
        auditResult: [{ required: true, message: "请选择审核结果", trigger: "change" }],
        rejectReason: [
          {
            validator: (_, value, callback) => {
              if (this.reviewForm.auditResult === 0 && (!value || value.trim() === "")) {
                callback(new Error("驳回时必须填写驳回原因"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      // 审核提交状态
      reviewSubmitting: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询专家评审列表 */
    async getList() {
      this.loading = true;
      try {
        const response = await listExpertReview(this.queryParams);
        this.meetingList = response.rows || response.data || [];
        this.total = response.total || 0;
      } catch (error) {
        console.error("获取专家评审列表失败:", error);
        this.$message.error("获取专家评审列表失败");
        this.meetingList = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查看专家 */
    handleViewExperts(row) {
      this.currentConsultation = row;
      this.expertDialogVisible = true;
    },
    /** 审核会诊 */
    handleReview(row) {
      // 检查是否已审核
      if (row.auditStatus === 1) {
        this.$message.warning("该会诊已审核，无法重复审核");
        return;
      }

      // 设置当前审核数据
      this.currentReviewData = { ...row };

      // 重置审核表单
      this.resetReviewForm();

      // 打开审核弹窗
      this.reviewDialogVisible = true;
    },

    /** 重置审核表单 */
    resetReviewForm() {
      this.reviewForm = {
        auditResult: null,
        rejectReason: "",
      };

      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.reviewForm) {
          this.$refs.reviewForm.clearValidate();
        }
      });
    },

    /** 取消审核 */
    handleCancelReview() {
      this.reviewDialogVisible = false;
      this.resetReviewForm();
    },

    /** 确认审核 */
    handleConfirmReview() {
      this.$refs.reviewForm.validate((valid) => {
        if (!valid) {
          return false;
        }

        // 二次确认
        const confirmMessage = this.reviewForm.auditResult === 1 ? "确认同意该会诊申请吗？" : "确认驳回该会诊申请吗？";

        this.$confirm(confirmMessage, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.submitReview();
          })
          .catch(() => {
            // 用户取消操作
          });
      });
    },

    /** 提交审核 */
    async submitReview() {
      this.reviewSubmitting = true;

      try {
        // 构建审核数据
        const reviewData = {
          id: this.currentReviewData.id,
          auditResult: this.reviewForm.auditResult,
          rejectReason: this.reviewForm.rejectReason,
          auditTime: new Date().toISOString(),
        };

        // 调用审核API接口
        await auditMeeting(reviewData);

        // 更新本地数据
        const meetingIndex = this.meetingList.findIndex((item) => item.id === this.currentReviewData.id);
        if (meetingIndex !== -1) {
          this.meetingList[meetingIndex].auditStatus = 1;
          this.meetingList[meetingIndex].auditTime = new Date().toLocaleString();
        }

        // 显示成功消息
        const successMessage = this.reviewForm.auditResult === 1 ? "审核通过成功" : "驳回成功";
        this.$message.success(successMessage);

        // 关闭弹窗
        this.reviewDialogVisible = false;
        this.resetReviewForm();

        // 刷新列表
        this.getList();
      } catch (error) {
        console.error("审核失败:", error);
        this.$message.error("审核操作失败，请重试");
      } finally {
        this.reviewSubmitting = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

/* 表格样式 */
.consultation-table {
  margin-top: 20px;
  border: 1px solid #e4e7ed;

  >>> .el-table__header-wrapper {
    background-color: #f5f7fa;
  }

  >>> .el-table__header th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
    border-bottom: 1px solid #e4e7ed;
  }

  >>> .el-table__body tr:hover > td {
    background-color: #f5f7fa;
  }

  >>> .el-table td {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 0;
  }
}

/* 操作按钮样式 */
.expert-btn {
  color: #409eff;
  margin-right: 10px;

  &:hover {
    color: #66b1ff;
  }
}

.review-btn {
  color: #409eff;

  &:hover {
    color: #66b1ff;
  }
}

/* 搜索表单样式 */
>>> .el-form--inline .el-form-item {
  margin-right: 20px;
  margin-bottom: 15px;
}

>>> .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

/* 分页样式 */
>>> .el-pagination {
  margin-top: 20px;
  text-align: right;
}

/* 审核弹窗样式 */
.review-dialog-content {
  .meeting-info-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;

    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-weight: 500;
        color: #606266;
        min-width: 120px;
        flex-shrink: 0;
      }

      .info-value {
        color: #303133;
        flex: 1;
        word-break: break-all;
      }
    }
  }
}

.dialog-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;

  .el-button {
    min-width: 80px;
    margin: 0 10px;
  }
}

/* 审核按钮禁用状态样式 */
.review-btn:disabled {
  color: #c0c4cc !important;
  cursor: not-allowed !important;
}

/* 表单样式优化 */
>>> .el-form-item__label {
  font-weight: 500;
}

>>> .el-radio-group {
  .el-radio {
    margin-right: 30px;

    .el-radio__label {
      font-size: 14px;
    }
  }
}

>>> .el-textarea {
  .el-textarea__inner {
    resize: vertical;
    min-height: 100px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .consultation-table {
    >>> .el-table__header th,
    >>> .el-table td {
      padding: 8px 5px;
      font-size: 12px;
    }
  }

  .review-dialog-content {
    .meeting-info-section {
      padding: 15px;

      .info-row {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 15px;

        .info-label {
          margin-bottom: 5px;
          min-width: auto;
        }
      }
    }
  }

  >>> .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }
}
</style>
