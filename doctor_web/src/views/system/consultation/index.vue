<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="患者名称：" prop="memName">
        <el-input
          v-model="queryParams.memName"
          placeholder=""
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" @click="handleQuery">查询</el-button>
        <el-button size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="consultationList" class="consultation-table">
      <el-table-column label="序号" align="center" width="60">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="患者名称" align="center" prop="memName" />
      <el-table-column label="疾病种类" align="center" prop="diseaseName" />
      <el-table-column label="会议类型" align="center" prop="consultType">
        <template slot-scope="scope">
          <span>{{ scope.row.consultType === 0 ? "线上" : "线下" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预期会议开始时间" align="center" prop="expectStartTime" width="160">
        <template slot-scope="scope">
          <span>{{ formatDateTime(scope.row.expectStartTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预期会议结束时间" align="center" prop="expectEndTime" width="160">
        <template slot-scope="scope">
          <span>{{ formatDateTime(scope.row.expectEndTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核时间" align="center" prop="auditTime" width="160">
        <template slot-scope="scope">
          <span>{{ formatDateTime(scope.row.auditTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="添加会议时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ formatDateTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会议地址/链接" align="center" prop="address" />
      <el-table-column label="审核状态" align="center" prop="auditStatus" width="100">
        <template slot-scope="scope">
          <span>{{ formatAuditStatus(scope.row.auditStatus) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" class="expert-btn" @click="handleViewExperts(scope.row)"
            >查看专家</el-button
          >
          <el-button size="mini" type="text" class="view-btn" @click="handleViewMeeting(scope.row)">查看会议</el-button>
          <el-button size="mini" type="text" class="supplement-btn" @click="handleSupplement(scope.row)"
            >补充会诊</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看专家弹窗 -->
    <ExpertViewDialog
      :visible.sync="expertDialogVisible"
      :consultation-data="currentConsultation"
      :meeting-id="currentConsultation && currentConsultation.id"
    />
  </div>
</template>

<script>
import { listConsultation } from "@/api/system/consultation";
import ExpertViewDialog from "@/components/ExpertViewDialog/index.vue";

export default {
  name: "Consultation",
  components: {
    ExpertViewDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会诊列表数据
      consultationList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        memName: null,
      },
      // 查看专家弹窗控制
      expertDialogVisible: false,
      // 当前选中的会诊记录
      currentConsultation: null,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询会诊列表 */
    async getList() {
      this.loading = true;
      try {
        const response = await listConsultation(this.queryParams);
        this.consultationList = response.rows || response.data || [];
        this.total = response.total || 0;
      } catch (error) {
        console.error("获取会诊列表失败:", error);
        this.$message.error("获取会诊列表失败");
        this.consultationList = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 查看专家 */
    handleViewExperts(row) {
      this.currentConsultation = row;
      this.expertDialogVisible = true;
    },
    /** 查看会议 */
    handleViewMeeting(row) {
      this.$router.push(`/system/meeting/view/index/${row.id}`);
    },
    /** 补充会诊 */
    handleSupplement(row) {
      this.$router.push(`/system/meeting/supplement/index/${row.id}`);
    },

    /** 格式化日期时间 */
    formatDateTime(dateTime) {
      if (!dateTime) return "-";

      // 如果是字符串，直接返回（后端已格式化）
      if (typeof dateTime === "string") {
        return dateTime;
      }

      // 如果是Date对象，格式化为 yyyy-MM-dd HH:mm:ss
      if (dateTime instanceof Date) {
        const year = dateTime.getFullYear();
        const month = String(dateTime.getMonth() + 1).padStart(2, "0");
        const day = String(dateTime.getDate()).padStart(2, "0");
        const hours = String(dateTime.getHours()).padStart(2, "0");
        const minutes = String(dateTime.getMinutes()).padStart(2, "0");
        const seconds = String(dateTime.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      return "-";
    },

    /** 格式化审核状态 */
    formatAuditStatus(status) {
      if (status === null || status === undefined) return "-";

      const statusMap = {
        0: "待审核",
        1: "已通过",
        2: "已驳回",
      };

      return statusMap[status] || "未知状态";
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

/* 表格样式 */
.consultation-table {
  margin-top: 20px;
  border: 1px solid #e4e7ed;

  >>> .el-table__header-wrapper {
    background-color: #f5f7fa;
  }

  >>> .el-table__header th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
    border-bottom: 1px solid #e4e7ed;
  }

  >>> .el-table__body tr:hover > td {
    background-color: #f5f7fa;
  }

  >>> .el-table td {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 0;
  }
}

/* 操作按钮样式 */
.expert-btn {
  color: #409eff;
  margin-right: 10px;

  &:hover {
    color: #66b1ff;
  }
}

.supplement-btn {
  color: #409eff;

  &:hover {
    color: #66b1ff;
  }
}

/* 搜索表单样式 */
>>> .el-form--inline .el-form-item {
  margin-right: 20px;
  margin-bottom: 15px;
}

>>> .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

/* 分页样式 */
>>> .el-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
