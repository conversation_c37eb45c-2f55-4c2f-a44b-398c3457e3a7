<template>
  <div class="app-container">
    <div class="meeting-create-page">
      <div class="page-header">
        <h2>发起会议</h2>
      </div>

      <!-- 患者基础信息 -->
      <PatientInfoCard :patient-info="patientInfo" />

      <!-- 会议信息表单 -->
      <MeetingInfoForm v-model="meetingInfo" ref="meetingForm" />

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import PatientInfoCard from "@/components/Meeting/PatientInfoCard";
import MeetingInfoForm from "@/components/Meeting/MeetingInfoForm";

import { sendMeeting } from "@/api/system/meeting";
import { getMemBaseInfo } from "@/api/system/patient";

export default {
  name: "MeetingCreate",
  components: {
    PatientInfoCard,
    MeetingInfoForm,
  },
  data() {
    return {
      // 患者信息
      patientInfo: {},
      // 会议信息
      meetingInfo: {
        selectedDoctors: [],
        meetingType: 0,
        expectedStartTime: "",
        expectedEndTime: "",
        meetingAddress: "",
      },
      // 提交状态
      submitting: false,
    };
  },
  created() {
    this.initializeData();
  },
  methods: {
    // 初始化数据
    async initializeData() {
      // 从路由参数获取患者ID
      const patientId = this.$route.query.patientId;
      if (patientId) {
        await this.loadPatientInfo(patientId);
      }
    },

    // 加载患者信息
    async loadPatientInfo(patientId) {
      try {
        const response = await getMemBaseInfo(patientId);

        this.patientInfo = response.data.webPatientVO;
      } catch (error) {
        console.error("获取患者信息失败:", error);
        this.$message.error("获取患者信息失败");
      }
    },

    // 处理取消
    handleCancel() {
      this.$confirm("确认取消创建会议吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$router.go(-1);
        })
        .catch(() => {});
    },

    // 处理提交
    async handleSubmit() {
      try {
        // 验证表单
        const meetingFormValid = await this.$refs.meetingForm.validate();

        if (!meetingFormValid) {
          this.$message.error("请完善表单信息");
          return;
        }

        this.submitting = true;

        // 构建发起会诊的提交数据
        const submitData = {
          address: this.meetingInfo.meetingAddress,
          consultType: parseInt(this.meetingInfo.meetingType), // 0线上 1线下
          expectEndTime: this.meetingInfo.expectedEndTime,
          expectStartTime: this.meetingInfo.expectedStartTime,
          idList: this.meetingInfo.selectedDoctors ? this.meetingInfo.selectedDoctors.map((doctor) => doctor.id) : [],
          diseaseCode: this.patientInfo.code || "",
          memId: this.patientInfo.id || 0,
          memName: this.patientInfo.realName || "",
        };

        // 提交数据到发起会诊接口
        await sendMeeting(submitData);

        this.$message.success("会诊发起成功");

        this.$refs.meetingForm.resetForm();
        // 跳转到会议列表或详情页
        this.$router.push("/case_list");
      } catch (error) {
        console.error("发起会诊失败:", error);
        this.$message.error("发起会诊失败");
      } finally {
        this.submitting = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.meeting-create-page {
  max-width: 1200px;
  margin: 0 auto;

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }

  .action-buttons {
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid #e4e7ed;
    margin-top: 20px;

    .el-button {
      min-width: 100px;
      margin: 0 10px;
    }
  }
}

.app-container {
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .meeting-create-page {
    max-width: 100%;

    .action-buttons {
      .el-button {
        width: 100px;
        margin: 5px;
      }
    }
  }
}
</style>
