<template>
  <div class="app-container">
    <div class="meeting-view-page">
      <div class="page-header">
        <h2>查看会议</h2>
      </div>

      <div v-loading="loading">
        <!-- 患者基础信息 -->
        <PatientInfoCard :patient-info="meetingData.patientInfo || {}" />

        <!-- 会议信息 -->
        <MeetingInfoForm v-model="meetingData.meetingInfo" :readonly="true" />

        <!-- 补充信息 -->
        <SupplementInfoForm v-model="meetingData.values" :readonly="true" :show-tooltip="false" />
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button @click="handleBack">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import PatientInfoCard from "@/components/Meeting/PatientInfoCard";
import MeetingInfoForm from "@/components/Meeting/MeetingInfoForm";
import SupplementInfoForm from "@/components/Meeting/SupplementInfoForm";
import { getMeeting } from "@/api/system/meeting";

export default {
  name: "MeetingView",
  components: {
    PatientInfoCard,
    MeetingInfoForm,
    SupplementInfoForm,
  },
  data() {
    return {
      loading: false,
      meetingData: {
        patientInfo: {},
        meetingInfo: {
          selectedDoctors: [],
          meetingType: 0,
          expectedStartTime: "",
          expectedEndTime: "",
          meetingAddress: "",
        },
        values: {},
      },
    };
  },
  created() {
    this.loadMeetingData();
  },
  methods: {
    // 加载会议数据
    async loadMeetingData() {
      const meetingId = this.$route.params.id;
      if (!meetingId) {
        this.$message.error("会议ID不能为空");
        this.$router.go(-1);
        return;
      }

      this.loading = true;
      try {
        const response = await getMeeting(meetingId);
        this.meetingData.patientInfo = response.data.webPatientVO;
        this.meetingData.meetingInfo = {
          selectedDoctorIds: response.data.meeting.doctors.map((e) => e.doctorId),
          meetingAddress: response.data.meeting.address,
          meetingType: response.data.meeting.consultType,
          expectedStartTime: response.data.meeting.expectStartTime,
          expectedEndTime: response.data.meeting.expectEndTime,
        };
        this.meetingData.values = response.data.values;
      } catch (error) {
        console.error("获取会议信息失败:", error);
        this.$message.error("获取会议信息失败");
      } finally {
        this.loading = false;
      }
    },

    // 返回上一页
    handleBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.meeting-view-page {
  max-width: 1200px;
  margin: 0 auto;

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }

  .action-buttons {
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid #e4e7ed;
    margin-top: 20px;

    .el-button {
      min-width: 100px;
    }
  }
}

.app-container {
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .meeting-view-page {
    max-width: 100%;
  }
}
</style>
