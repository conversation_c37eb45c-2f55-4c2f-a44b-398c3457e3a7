# 会诊模块字段映射对照表

## 字段映射更新总结

根据后端API字段定义，已完成 `consultation/index.vue` 文件的字段映射更新。

## 后端字段定义
```java
private Long id;                    // 主键ID
private Long memId;                 // 患者ID
private String memName;             // 患者姓名
private String diseaseName;         // 疾病种类
private Integer consultType;        // 会议类型 (0: 线上, 1: 线下)
private Date expectStartTime;       // 预期开始时间
private Date expectEndTime;         // 预期结束时间
private Date createTime;            // 创建时间
private Date auditTime;             // 审核时间
private String record;              // 记录
private String address;             // 会议地址/链接
private Integer auditStatus;        // 审核状态 (0: 待审核, 1: 已通过, 2: 已驳回)
```

## 前端字段映射对照

### 搜索表单字段映射
| 前端原字段 | 后端字段 | 说明 |
|-----------|---------|------|
| patientName | memName | 患者姓名搜索字段 |
| - | auditStatus | 新增审核状态筛选 |

### 表格列字段映射
| 表格列标题 | 前端原字段 | 后端字段 | 数据类型 | 格式化方法 |
|-----------|-----------|---------|---------|-----------|
| 患者名称 | patientName | memName | String | - |
| 疾病种类 | diseaseType | diseaseName | String | - |
| 会议类型 | meetingType | consultType | Integer | 0: 线上, 1: 线下 |
| 预期会议开始时间 | expectedStartTime | expectStartTime | Date | formatDateTime() |
| 预期会议结束时间 | expectedEndTime | expectEndTime | Date | formatDateTime() |
| 审核时间 | auditTime | auditTime | Date | formatDateTime() |
| 创建时间 | addMeetingTime | createTime | Date | formatDateTime() |
| 会议地址/链接 | meetingAddress | address | String | - |
| 审核状态 | - | auditStatus | Integer | formatAuditStatus() |

### 查询参数映射
| 前端原参数 | 后端参数 | 类型 | 说明 |
|-----------|---------|------|------|
| patientName | memName | String | 患者姓名模糊查询 |
| - | auditStatus | Integer | 审核状态精确查询 |
| pageNum | pageNum | Integer | 页码 |
| pageSize | pageSize | Integer | 每页大小 |

## 数据格式化方法

### 时间格式化 (formatDateTime)
- **输入**: Date对象或字符串
- **输出**: yyyy-MM-dd HH:mm:ss 格式字符串
- **处理逻辑**:
  - 空值返回 "-"
  - 字符串直接返回（后端已格式化）
  - Date对象格式化为标准格式

### 会议类型格式化
- **字段**: consultType
- **映射**: 0 → "线上", 1 → "线下"

### 审核状态格式化 (formatAuditStatus)
- **字段**: auditStatus
- **映射**: 
  - 0 → "待审核"
  - 1 → "已通过" 
  - 2 → "已驳回"
  - null/undefined → "-"
  - 其他值 → "未知状态"

## 更新内容详细说明

### 1. 搜索表单更新
```vue
<!-- 原字段 -->
<el-form-item label="患者名称：" prop="patientName">
  <el-input v-model="queryParams.patientName" />
</el-form-item>

<!-- 更新后 -->
<el-form-item label="患者名称：" prop="memName">
  <el-input v-model="queryParams.memName" />
</el-form-item>

<!-- 新增审核状态筛选 -->
<el-form-item label="审核状态：" prop="auditStatus">
  <el-select v-model="queryParams.auditStatus">
    <el-option label="待审核" :value="0" />
    <el-option label="已通过" :value="1" />
    <el-option label="已驳回" :value="2" />
  </el-select>
</el-form-item>
```

### 2. 表格列更新
```vue
<!-- 字段名更新示例 -->
<el-table-column label="患者名称" prop="memName" />
<el-table-column label="疾病种类" prop="diseaseName" />
<el-table-column label="会议类型" prop="consultType">
  <template slot-scope="scope">
    <span>{{ scope.row.consultType === 0 ? "线上" : "线下" }}</span>
  </template>
</el-table-column>

<!-- 时间格式化示例 -->
<el-table-column label="预期会议开始时间" prop="expectStartTime">
  <template slot-scope="scope">
    <span>{{ formatDateTime(scope.row.expectStartTime) }}</span>
  </template>
</el-table-column>

<!-- 新增审核状态列 -->
<el-table-column label="审核状态" prop="auditStatus">
  <template slot-scope="scope">
    <span>{{ formatAuditStatus(scope.row.auditStatus) }}</span>
  </template>
</el-table-column>
```

### 3. 查询参数更新
```javascript
// 原查询参数
queryParams: {
  pageNum: 1,
  pageSize: 10,
  patientName: null,
}

// 更新后查询参数
queryParams: {
  pageNum: 1,
  pageSize: 10,
  memName: null,
  auditStatus: null,
}
```

## API 接口兼容性

### 请求参数格式
```javascript
// GET /system/consultation/list
{
  pageNum: 1,
  pageSize: 10,
  memName: "患者姓名",      // 模糊查询
  auditStatus: 0           // 精确查询
}
```

### 响应数据格式
```javascript
{
  "code": 200,
  "msg": "success",
  "rows": [
    {
      "id": 1,
      "memId": 123,
      "memName": "李明照",
      "diseaseName": "结构缺陷",
      "consultType": 0,
      "expectStartTime": "2024-01-01 10:00:00",
      "expectEndTime": "2024-01-01 12:00:00",
      "createTime": "2024-01-01 09:00:00",
      "auditTime": "2024-01-01 09:30:00",
      "address": "武汉洪山区光谷总部国际时代二期2栋801-3",
      "auditStatus": 1
    }
  ],
  "total": 100
}
```

## 测试建议

### 功能测试
- [ ] 患者姓名搜索功能
- [ ] 审核状态筛选功能
- [ ] 分页功能
- [ ] 时间格式显示
- [ ] 会议类型显示
- [ ] 审核状态显示

### 数据兼容性测试
- [ ] 空值处理（时间为null）
- [ ] 异常数据处理（状态值超出范围）
- [ ] 时间格式兼容性（字符串vs Date对象）

### 界面测试
- [ ] 表格列宽度适配
- [ ] 长文本显示（地址字段）
- [ ] 响应式布局

## 注意事项

1. **时间格式**: 后端使用两种时间格式模式，前端统一处理为 yyyy-MM-dd HH:mm:ss
2. **数值映射**: consultType 和 auditStatus 都是数值类型，需要正确映射显示文本
3. **空值处理**: 所有格式化方法都包含空值处理逻辑
4. **向后兼容**: 保持了原有的用户界面和交互逻辑
5. **扩展性**: 审核状态映射支持未来可能的新状态值

## 后续优化建议

1. **状态配置化**: 将状态映射提取为配置文件
2. **时间组件**: 考虑使用专门的时间格式化组件
3. **国际化**: 为状态文本添加国际化支持
4. **缓存优化**: 对静态数据（如状态选项）添加缓存
